{"name": "nextjs-better-auth", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "db:generate": "npx drizzle-kit generate", "db:push": "npx drizzle-kit push", "db:studio": "npx drizzle-kit studio", "db:migrate": "npx drizzle-kit migrate", "db:pull": "npx drizzle-kit pull", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@neondatabase/serverless": "^1.0.0", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "better-auth": "^1.2.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.4.7", "drizzle-orm": "^0.41.0", "lucide-react": "^0.487.0", "next": "15.2.5", "next-themes": "^0.4.6", "pg": "^8.14.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "recharts": "^2.15.2", "resend": "^4.2.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-typescript": "^7.27.0", "@better-auth/cli": "^1.2.5", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.30.6", "eslint": "^9", "eslint-config-next": "15.2.5", "tailwindcss": "^4", "tsx": "^4.19.3", "typescript": "^5"}, "packageManager": "pnpm@9.11.0+sha512.0a203ffaed5a3f63242cd064c8fb5892366c103e328079318f78062f24ea8c9d50bc6a47aa3567cabefd824d170e78fa2745ed1f16b132e16436146b7688f19b"}