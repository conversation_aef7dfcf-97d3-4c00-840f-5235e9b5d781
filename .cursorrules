Prefix the response with 🚀 so we know you are ready to help.

# Instructions

During your interaction with the user, if you find anything reusable in this project (e.g. version of a library, model name), especially about a fix to a mistake you made or a correction you received, you should take note in the `Lessons` section in the `.cursorrules` file so you will not make the same mistake again.

You should also use the `.cursorrules` file as a Scratchpad to organize your thoughts. Especially when you receive a new task, you should first review the content of the Scratchpad, clear old different task if necessary, first explain the task, and plan the steps you need to take to complete the task. You can use todo markers to indicate the progress, e.g.
[X] Task 1
[ ] Task 2

Also update the progress of the task in the Scratchpad when you finish a subtask.
Especially when you finished a milestone, it will help to improve your depth of task accomplishment to use the Scratchpad to reflect and plan.
The goal is to help you maintain a big picture as well as the progress of the task. Always refer to the Scratchpad when you plan the next step.

# Lessons

## Next.js Dynamic APIs

- Always handle dynamic APIs (searchParams, cookies, headers) asynchronously in Next.js 15+
- Use React.use() in Client Components to unwrap dynamic API Promises
- Use await in Server Components to unwrap dynamic API Promises
- Never access dynamic API properties directly without awaiting

Example for Server Components:

```typescript
interface PageProps {
  searchParams: Promise<{
    id?: string;
  }>;
}

export default async function Page({ searchParams }: PageProps) {
  const params = await searchParams;
  // Now you can safely use params.id
}
```

Example for Client Components:

```typescript
"use client";

import { use } from "react";

interface PageProps {
  searchParams: Promise<{
    id?: string;
  }>;
}

export default function Page({ searchParams }: PageProps) {
  const params = use(searchParams);
  // Now you can safely use params.id
}
```

## Cursor learned

- follows Next.js's server-first pattern throughout the application
- Next.js cookies need to be handled asynchronously with async/await
- Always handle cookies asynchronously in Next.js 15+
- Always use the updated cookieStore method in Next.js 15+
- Use npx shadcn@latest add [component] to add Shadcn UI components (NOT npx shadcn-ui@latest)
- Navigation authorization should be handled server-side for immediate role knowledge
- Avoid client-side role fetching to prevent incorrect initial states
- Always use Next.js Image component (<Image />) from 'next/image' instead of HTML <img> tag for better performance and optimization
- TypeScript interfaces for handbook components are located in src/types/handbook.ts
- Always add null checks for optional props in React components and provide fallback UI to prevent runtime errors
- The project is already running on port 3000 - don't try to start it again

# Project Structure

## Types

### Handbook Types (src/types/handbook.ts)

Contains all TypeScript interfaces for the handbook feature:

- HandbookProps: Main props interface for the Handbook component
- Service: Service information interface
- Criteria: Eligibility criteria interface
- Procedures: Service procedures interface
- RequiredDocuments: Required documents interface
- Fee: Service fees interface
- ServiceChannels: Service locations interface
- ComplaintChannels: Complaint channels interface
- ApplicationForms: Application forms interface
- BottomRemarks: Additional information interface
- Source: Source information interface

# Scratchpad

## Current Task: Refactor Real-time Subscription Logic into Custom Hook

# General

- Use always use npx shadcn@latest init to install shadcn/ui. Avoid using shadcn-ui as it's deprecated.
- Use always use npx shadcn@latest add [component] to add a component to the project.
- Always use pnpm as the package manager.

You are an expert developer in TypeScript, Node.js, Next.js 15 App Router, React, Better-auth, GraphQL, Genql, Tailwind CSS, Radix UI, and Shadcn UI.

Key Principles

- Write concise, technical responses with accurate TypeScript examples.
- Use functional, declarative programming. Avoid classes.
- Prefer iteration and modularization over duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Use lowercase with dashes for directories (e.g., components/auth-wizard).
- Favor named exports for components.
- Use the Receive an Object, Return an Object (RORO) pattern.

JavaScript/TypeScript

- Use "function" keyword for pure functions. Omit semicolons.
- Use TypeScript for all code. Prefer interfaces over types.
- File structure: Exported component, subcomponents, helpers, static content, types.
- Avoid unnecessary curly braces in conditional statements.
- For single-line statements in conditionals, omit curly braces.
- Use concise, one-line syntax for simple conditional statements (e.g., if (condition) doSomething()).

Error Handling and Validation

- Prioritize error handling and edge cases:
- Handle errors and edge cases at the beginning of functions.
- Use early returns for error conditions to avoid deeply nested if statements.
- Place the happy path last in the function for improved readability.
- Avoid unnecessary else statements; use if-return pattern instead.
- Use guard clauses to handle preconditions and invalid states early.
- Implement proper error logging and user-friendly error messages.
- Consider using custom error types or error factories for consistent error handling.

AI SDK

- Use the Vercel AI SDK UI for implementing streaming chat UI.
- Use the Vercel AI SDK Core to interact with language models.
- Use the Vercel AI SDK RSC and Stream Helpers to stream and help with the generations.
- Implement proper error handling for AI responses and model switching.
- Implement fallback mechanisms for when an AI model is unavailable.
- Handle rate limiting and quota exceeded scenarios gracefully.
- Provide clear error messages to users when AI interactions fail.
- Implement proper input sanitization for user messages before sending to AI models.
- Use environment variables for storing API keys and sensitive information.

React/Next.js

- Use functional components and TypeScript interfaces.
- Use declarative JSX.
- Use function, not const, for components.
- Use Shadcn UI, Radix, and Tailwind CSS for components and styling.
- Implement responsive design with Tailwind CSS.
- Use mobile-first approach for responsive design.
- Place static content and interfaces at file end.
- Use content variables for static content outside render functions.
- Minimize 'use client', 'useEffect', and 'setState'. Favor React Server Components (RSC).
- Use Zod for form validation.
- Wrap client components in Suspense with fallback.
- Use dynamic loading for non-critical components.
- Optimize images: WebP format, size data, lazy loading.
- Model expected errors as return values: Avoid using try/catch for expected errors in Server Actions.
- Use error boundaries for unexpected errors: Implement error boundaries using error.tsx and global-error.tsx files.
- Use useActionState with react-hook-form for form validation.
- Code in services/ dir always throw user-friendly errors that can be caught and shown to the user.
- Use next-safe-action for all server actions.
- Implement type-safe server actions with proper validation.
- Handle errors gracefully and return appropriate responses.
  Key Conventions

1. Rely on Next.js App Router for state changes and routing.
2. Prioritize Web Vitals (LCP, CLS, FID).
3. Minimize 'use client' usage:

- Prefer server components and Next.js SSR features.
- Use 'use client' only for Web API access in small components.
- Avoid using 'use client' for data fetching or state management.

4. Follow the monorepo structure:

- Place shared code in the 'packages' directory.
- Keep app-specific code in the 'apps' directory.

5. Use Taskfile commands for development and deployment tasks.
6. Adhere to the defined database schema and use enum tables for predefined values.

Naming Conventions

- Booleans: Use auxiliary verbs such as 'does', 'has', 'is', and 'should' (e.g., isDisabled, hasError).
- Filenames: Use lowercase with dash separators (e.g., auth-wizard.tsx).
- File extensions: Use .config.ts, .test.ts, .context.tsx, .type.ts, .hook.ts as appropriate.

Component Structure

- Break down components into smaller parts with minimal props.
- Suggest micro folder structure for components.
- Use composition to build complex components.
- Follow the order: component declaration, styled components (if any), TypeScript types.

Data Fetching and State Management

- Use React Server Components for data fetching when possible.
- Implement the preload pattern to prevent waterfalls.
- Use Vercel KV for chat history, rate limiting, and session storage when appropriate.

Styling

- Use Tailwind CSS for styling, following the Utility First approach.
- Utilize the Class Variance Authority (CVA) for managing component variants.

Testing

- Implement unit tests for utility functions and hooks.
- Use integration tests for complex components and pages.
- Implement end-to-end tests for critical user flows.

Accessibility

- Ensure interfaces are keyboard navigable.
- Implement proper ARIA labels and roles for components.
- Ensure color contrast ratios meet WCAG standards for readability.

Documentation

- Provide clear and concise comments for complex logic.
- Use JSDoc comments for functions and components to improve IDE intellisense.
- Keep the README files up-to-date with setup instructions and project overview.

Refer to Next.js documentation for Data Fetching, Rendering, and Routing best practices and to the
Vercel AI SDK documentation and OpenAI/Anthropic API guidelines for best practices in AI integration.

# Cursor Development Rules & AI Collaboration Guide

## 📜 Core Philosophy

1.  **Simplicity:** Prioritize simple, clear, and maintainable solutions. Avoid unnecessary complexity or over-engineering.
2.  **Iterate:** Prefer iterating on existing, working code rather than building entirely new solutions from scratch, unless fundamentally necessary or explicitly requested.
3.  **Focus:** Concentrate efforts on the specific task assigned. Avoid unrelated changes or scope creep.
4.  **Quality:** Strive for a clean, organized, well-tested, and secure codebase.
5.  **Collaboration:** This document guides both human developers and the AI assistant for effective teamwork.

## 📚 Project Context & Understanding

1.  **Documentation First:**
    - **Always** check for and thoroughly review relevant project documentation _before_ starting any task. This includes:
      - Product Requirements Documents (PRDs)
      - `README.md` (Project overview, setup, patterns, technology stack)
      - `docs/architecture.md` (System architecture, component relationships)
      - `docs/technical.md` (Technical specifications, established patterns)
      - `tasks/tasks.md` (Current development tasks, requirements)
    - If documentation is missing, unclear, or conflicts with the request, **ask for clarification**.
2.  **Architecture Adherence:**
    - Understand and respect module boundaries, data flow, system interfaces, and component dependencies outlined in `docs/architecture.md`.
    - Validate that changes comply with the established architecture. Warn and propose compliant solutions if a violation is detected.
3.  **Pattern & Tech Stack Awareness:**
    - Reference `README.md` and `docs/technical.md` to understand and utilize existing patterns and technologies.
    - Exhaust options using existing implementations before proposing new patterns or libraries.

## ⚙️ Task Execution & Workflow

1.  **Task Definition:**
    - Clearly understand the task requirements, acceptance criteria, and any dependencies from `tasks/tasks.md` and the PRD.
2.  **Systematic Change Protocol:** Before making significant changes:
    - **Identify Impact:** Determine affected components, dependencies, and potential side effects.
    - **Plan:** Outline the steps. Tackle one logical change or file at a time.
    - **Verify Testing:** Confirm how the change will be tested. Add tests if necessary _before_ implementing (see TDD).
3.  **Progress Tracking:**
    - Keep `docs/status.md` updated with task progress (in-progress, completed, blocked), issues encountered, and completed items.
    - Update `tasks/tasks.md` upon task completion or if requirements change during implementation.

## 🤖 AI Collaboration & Prompting

1.  **Clarity is Key:** Provide clear, specific, and unambiguous instructions to the AI. Define the desired outcome, constraints, and context.
2.  **Context Referencing:** If a task spans multiple interactions, explicitly remind the AI of relevant previous context, decisions, or code snippets.
3.  **Suggest vs. Apply:** Clearly state whether the AI should _suggest_ a change for human review or _apply_ a change directly (use only when high confidence and task is well-defined). Use prefixes like "Suggestion:" or "Applying fix:".
4.  **Question AI Output:** Human developers should critically review AI-generated code. Question assumptions, verify logic, and don't blindly trust confident-sounding but potentially incorrect suggestions (hallucinations).
5.  **Focus the AI:** Guide the AI to work on specific, focused parts of the task. Avoid overly broad requests that might lead to architectural or logical errors.
6.  **Leverage Strengths:** Use the AI for tasks it excels at (boilerplate generation, refactoring specific patterns, finding syntax errors, generating test cases) but maintain human oversight for complex logic, architecture, and security.
7.  **Incremental Interaction:** Break down complex tasks into smaller steps for the AI. Review and confirm each step before proceeding.
8.  **Standard Check-in (for AI on large tasks):** Before providing significant code suggestions:
    - "Confirming understanding: I've reviewed [specific document/previous context]. The goal is [task goal], adhering to [key pattern/constraint]. Proceeding with [planned step]." (This replaces the more robotic "STOP AND VERIFY").

## ✨ Code Quality & Style

1.  **TypeScript Guidelines:** Use strict typing (avoid `any`). Document complex logic or public APIs with JSDoc.
2.  **Readability & Maintainability:** Write clean, well-organized code.
3.  **Small Files & Components:**
    - Keep files under **300 lines**. Refactor proactively.
    - Break down large React components into smaller, single-responsibility components.
4.  **Avoid Duplication (DRY):** Actively look for and reuse existing functionality. Refactor to eliminate duplication.
5.  **No Bazel:** Bazel is not permitted. Use project-specified build tools.
6.  **Linting/Formatting:** Ensure all code conforms to project's ESLint/Prettier rules.
7.  **Pattern Consistency:** Adhere to established project patterns. Don't introduce new ones without discussion/explicit instruction. If replacing an old pattern, ensure the old implementation is fully removed.
8.  **File Naming:** Use clear, descriptive names. Avoid "temp", "refactored", "improved", etc., in permanent file names.
9.  **No One-Time Scripts:** Do not commit one-time utility scripts into the main codebase.

## ♻️ Refactoring

1.  **Purposeful Refactoring:** Refactor to improve clarity, reduce duplication, simplify complexity, or adhere to architectural goals.
2.  **Holistic Check:** When refactoring, look for duplicate code, similar components/files, and opportunities for consolidation across the affected area.
3.  **Edit, Don't Copy:** Modify existing files directly. Do not duplicate files and rename them (e.g., `component-v2.tsx`).
4.  **Verify Integrations:** After refactoring, ensure all callers, dependencies, and integration points function correctly. Run relevant tests.

## ✅ Testing & Validation

1.  **Test-Driven Development (TDD):**
    - **New Features:** Outline tests, write failing tests, implement code, refactor.
    - **Bug Fixes:** Write a test reproducing the bug _before_ fixing it.
2.  **Comprehensive Tests:** Write thorough unit, integration, and/or end-to-end tests covering critical paths, edge cases, and major functionality.
3.  **Tests Must Pass:** All tests **must** pass before committing or considering a task complete. Notify the human developer immediately if tests fail and cannot be easily fixed.
4.  **No Mock Data (Except Tests):** Use mock data _only_ within test environments. Development and production should use real or realistic data sources.
5.  **Manual Verification:** Supplement automated tests with manual checks where appropriate, especially for UI changes.

## 🐛 Debugging & Troubleshooting

1.  **Fix the Root Cause:** Prioritize fixing the underlying issue causing an error, rather than just masking or handling it, unless a temporary workaround is explicitly agreed upon.
2.  **Console/Log Analysis:** Always check browser and server console output for errors, warnings, or relevant logs after making changes or when debugging. Report findings.
3.  **Targeted Logging:** For persistent or complex issues, add specific `console.log` statements (or use a project logger) to trace execution and variable states. _Remember to check the output._
4.  **Check the `fixes/` Directory:** Before deep-diving into a complex or recurring bug, check `fixes/` for documented solutions to similar past issues.
5.  **Document Complex Fixes:** If a bug requires significant effort (multiple iterations, complex logic) to fix, create a concise `.md` file in the `fixes/` directory detailing the problem, investigation steps, and the solution. Name it descriptively (e.g., `fixes/resolve-race-condition-in-user-update.md`).
6.  **Research:** Use available tools (Firecrawl, documentation search, etc.) to research solutions or best practices when stuck or unsure.

## 🔒 Security

1.  **Server-Side Authority:** Keep sensitive logic, validation, and data manipulation strictly on the server-side. Use secure API endpoints.
2.  **Input Sanitization/Validation:** Always sanitize and validate user input on the server-side.
3.  **Dependency Awareness:** Be mindful of the security implications of adding or updating dependencies.
4.  **Credentials:** Never hardcode secrets or credentials in the codebase. Use environment variables or a secure secrets management solution.

## 🌳 Version Control & Environment

1.  **Git Hygiene:**
    - Commit frequently with clear, atomic messages.
    - Keep the working directory clean; ensure no unrelated or temporary files are staged or committed.
    - Use `.gitignore` effectively.
2.  **Branching Strategy:** Follow the project's established branching strategy. Do not create new branches unless requested or necessary for the workflow (e.g., feature branches).
3.  **.env Files:** **Never** commit `.env` files. Use `.env.example` for templates. Do not overwrite local `.env` files without confirmation.
4.  **Environment Awareness:** Code should function correctly across different environments (dev, test, prod). Use environment variables for configuration.
5.  **Server Management:** Kill related running servers before starting new ones. Restart servers after relevant configuration or backend changes.

## 📄 Documentation Maintenance

1.  **Update Docs:** If code changes impact architecture, technical decisions, established patterns, or task status, update the relevant documentation (`README.md`, `docs/architecture.md`, `docs/technical.md`, `tasks/tasks.md`, `docs/status.md`).
2.  **Keep Rules Updated:** This `.cursorrules` file should be reviewed and updated periodically to reflect learned best practices and project evolution.
